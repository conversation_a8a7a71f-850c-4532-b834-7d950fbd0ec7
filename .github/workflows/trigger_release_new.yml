name: Trigger Release (New)

on:
  # Run every day at 23:15 UTC
  # TODO: Disabled cron for now, but uncomment
  # once replaced the old release workflow.
  # schedule:
  #   - cron: '15 23 * * *'
  # Run manually
  workflow_dispatch:
    inputs:
      releaseType:
        description: Release Type
        required: true
        type: choice
        # Cron job will run canary release
        default: canary
        options:
          - canary
          - stable
          - release-candidate

      force:
        description: Forced Release
        default: false
        type: boolean

concurrency: ${{ github.workflow }}-${{ github.ref }}

env:
  NAPI_CLI_VERSION: 2.18.4
  TURBO_VERSION: 2.3.3
  NODE_LTS_VERSION: 20

permissions:
  # To create PR
  pull-requests: write

jobs:
  start:
    if: github.repository_owner == 'vercel'
    runs-on: ubuntu-latest
    env:
      NEXT_TELEMETRY_DISABLED: 1
      # we build a dev binary for use in CI so skip downloading
      # canary next-swc binaries in the monorepo
      NEXT_SKIP_NATIVE_POSTINSTALL: 1

    environment: release-${{ github.event.inputs.releaseType }}
    steps:
      - name: Setup node
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_LTS_VERSION }}
          check-latest: true

      # Since actions/checkout won't include the latest tag information,
      # use the old clone workflow while still preserving branch specific
      # checkout behavior to support backports.
      # x-ref: https://github.com/vercel/next.js/pull/63167
      - name: Clone Next.js repository
        run: git clone https://github.com/vercel/next.js.git --depth=25 --single-branch --branch ${GITHUB_REF_NAME:-canary} .

      - name: Check token
        run: gh auth status
        env:
          GITHUB_TOKEN: ${{ secrets.RELEASE_BOT_GITHUB_TOKEN }}

      - name: Get commit of the latest tag
        run: echo "LATEST_TAG_COMMIT=$(git rev-list -n 1 $(git describe --tags --abbrev=0))" >> $GITHUB_ENV

      - name: Get latest commit
        run: echo "LATEST_COMMIT=$(git rev-parse HEAD)" >> $GITHUB_ENV

      - name: Check if new commits since last tag
        if: ${{ github.event.inputs.releaseType != 'stable' && github.event.inputs.force != true }}
        run: |
          if [ "$LATEST_TAG_COMMIT" = "$LATEST_COMMIT" ]; then
            echo "No new commits. Exiting..."
            exit 1
          fi

      # https://github.com/actions/virtual-environments/issues/1187
      - name: tune linux network
        run: sudo ethtool -K eth0 tx off rx off

      - name: Setup corepack
        run: |
          npm i -g corepack@0.31
          corepack enable
          pnpm --version

      - id: get-store-path
        run: echo STORE_PATH=$(pnpm store path) >> $GITHUB_OUTPUT

      - uses: actions/cache@v4
        timeout-minutes: 5
        id: cache-pnpm-store
        with:
          path: ${{ steps.get-store-path.outputs.STORE_PATH }}
          key: pnpm-store-${{ hashFiles('pnpm-lock.yaml') }}
          restore-keys: |
            pnpm-store-
            pnpm-store-${{ hashFiles('pnpm-lock.yaml') }}

      - run: pnpm install
      - run: pnpm run build

      - name: Create Release Pull Request
        id: changesets
        uses: changesets/action@v1
        with:
          version: pnpm ci:version
        env:
          GITHUB_TOKEN: ${{ secrets.RELEASE_BOT_GITHUB_TOKEN }}
          RELEASE_TYPE: ${{ github.event.inputs.releaseType }}

      # Add label to verify the PR is created from this workflow.
      - name: Add label to PR
        if: steps.changesets.outputs.pullRequestNumber
        run: 'gh pr edit ${{ steps.changesets.outputs.pullRequestNumber }} --add-label "created-by: CI"'
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
