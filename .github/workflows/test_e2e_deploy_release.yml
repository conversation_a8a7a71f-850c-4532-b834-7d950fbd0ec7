name: test-e2e-deploy-release

on:
  # run on every release/prerelease
  release:
    types: [published]
  # allow triggering manually as well
  workflow_dispatch:
    inputs:
      nextVersion:
        description: canary or custom tarball URL
        default: canary
        type: string
      vercelCliVersion:
        description: Version of Vercel CLI to use
        default: 'vercel@latest'
        type: string

env:
  VERCEL_TEST_TEAM: vtest314-next-e2e-tests
  VERCEL_TEST_TOKEN: ${{ secrets.VERCEL_TEST_TOKEN }}
  DATADOG_API_KEY: ${{ secrets.DATA_DOG_API_KEY }}
  TURBO_TEAM: 'vercel'
  TURBO_CACHE: 'remote:rw'
  TURBO_API: ${{ secrets.HOSTED_TURBO_API }}
  TURBO_TOKEN: ${{ secrets.HOSTED_TURBO_TOKEN }}
  DD_ENV: 'ci'

run-name: test-e2e-deploy ${{ inputs.nextVersion || 'canary' }}

jobs:
  setup:
    runs-on: ubuntu-latest
    if: github.repository_owner == 'vercel'
    steps:
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_LTS_VERSION }}
          check-latest: true

      - name: Setup pnpm
        run: |
          npm i -g corepack@0.31
          corepack enable

      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 25

      - name: Setup test project
        run: |
          pnpm install
          pnpm run build
          node scripts/run-e2e-test-project-reset.mjs

  test-deploy:
    name: test deploy
    needs: setup
    uses: ./.github/workflows/build_reusable.yml
    secrets: inherit
    strategy:
      fail-fast: false
      matrix:
        group: [1/6, 2/6, 3/6, 4/6, 5/6, 6/6]
    with:
      afterBuild: npm i -g vercel@latest && NEXT_E2E_TEST_TIMEOUT=240000 NEXT_TEST_MODE=deploy NEXT_EXTERNAL_TESTS_FILTERS="test/deploy-tests-manifest.json" NEXT_TEST_VERSION="${{ github.event.inputs.nextVersion || 'canary' }}" VERCEL_CLI_VERSION="${{ github.event.inputs.vercelCliVersion || 'vercel@latest' }}" node run-tests.js --timings -g ${{ matrix.group }} -c 2 --type e2e
      skipNativeBuild: 'yes'
      skipNativeInstall: 'no'
      stepName: 'test-deploy-${{ matrix.group }}'
      timeout_minutes: 180
      runs_on_labels: '["ubuntu-latest"]'

  report-test-results-to-datadog:
    needs: test-deploy
    if: ${{ always() }}

    runs-on: ubuntu-latest
    name: report test results to datadog
    steps:
      - name: Download test report artifacts
        id: download-test-reports
        uses: actions/download-artifact@v4
        with:
          pattern: test-reports-*
          path: test
          merge-multiple: true

      - name: Upload test report to datadog
        run: |
          if [ -d ./test/test-junit-report ]; then
            DD_ENV=ci npx @datadog/datadog-ci@2.23.1 junit upload --tags test.type:deploy --service nextjs ./test/test-junit-report
          fi

  sync-repositories:
    needs: test-deploy
    if: ${{ always() && github.repository_owner == 'vercel' && github.event_name == 'release' }}
    runs-on: ubuntu-latest
    strategy:
      fail-fast: false
      matrix:
        include:
          - repo: front
            workflow_id: cron-update-next.yml
            workflow_url: https://github.com/vercel/front/actions/workflows/cron-update-next.yml?query=event%3Aworkflow_dispatch
          - repo: v0
            workflow_id: update-next.yml
            workflow_url: https://github.com/vercel/v0/actions/workflows/update-next.yml?query=event%3Aworkflow_dispatch
    steps:
      - uses: actions/checkout@v4
      - id: nextPackageInfo
        name: Get `next` package info
        run: |
          cd packages/next 
          {
            echo 'value<<EOF'
            cat package.json
            echo EOF
          } >> "$GITHUB_OUTPUT"
      - id: version
        name: Extract `next` version
        run: echo 'value=${{ fromJson(steps.nextPackageInfo.outputs.value).version }}' >> "$GITHUB_OUTPUT"
      - id: test-result
        name: Set test result variable
        run: echo 'immediately-close=${{ needs.test-deploy.result != 'success' && 'true' || 'false' }}' >> "$GITHUB_OUTPUT"
      - name: Check token
        run: gh auth status
        env:
          GITHUB_TOKEN: ${{ secrets.GH_UPDATE_NEXT_WORKFLOW_TRIGGER }}
      - uses: actions/github-script@v7
        name: Check if target workflow is enabled
        id: check-workflow-enabled
        with:
          retries: 3
          retry-exempt-status-codes: 400,401,404
          github-token: ${{ secrets.GH_UPDATE_NEXT_WORKFLOW_TRIGGER }}
          result-encoding: string
          script: |
            try {
              const response = await github.request(
                "GET /repos/{owner}/{repo}/actions/workflows/{workflow_id}",
                {
                  owner: "vercel",
                  repo: "${{ matrix.repo }}",
                  workflow_id: "${{ matrix.workflow_id }}",
                }
              );
              
              const isEnabled = response.data.state === 'active';
              console.info(`Target workflow state: ${response.data.state}`);
              console.info(`Target workflow enabled: ${isEnabled}`);
              
              return isEnabled ? 'true' : 'false';
            } catch (error) {
              console.error('Error checking workflow status:', error);
              return 'false';
            }
      - uses: actions/github-script@v7
        name: Trigger vercel/${{ matrix.repo }} sync
        id: trigger-sync
        if: steps.check-workflow-enabled.outputs.result == 'true'
        with:
          retries: 3
          retry-exempt-status-codes: 400,401,404
          # Default github token cannot dispatch events to the remote repo, it should be
          # a PAT with Actions write access (https://docs.github.com/en/rest/actions/workflows?apiVersion=2022-11-28#create-a-workflow-dispatch-event)
          github-token: ${{ secrets.GH_UPDATE_NEXT_WORKFLOW_TRIGGER }}
          # Note `workflow_id` and `inputs` are contract between vercel/${{ matrix.repo }},
          # if these need to be changed both side should be updated accordingly.
          script: |
            const immediatelyClose = '${{ steps.test-result.outputs.immediately-close }}';
            const inputs = {
              version: "${{ steps.version.outputs.value }}",
            };

            if (immediatelyClose === 'true') {
              inputs['immediately-close'] = 'true';
            }

            await github.request(
              "POST /repos/{owner}/{repo}/actions/workflows/{workflow_id}/dispatches",
              {
                owner: "vercel",
                repo: "${{ matrix.repo }}",
                workflow_id: "${{ matrix.workflow_id }}",
                ref: "main",
                inputs: inputs,
              }
            );
            // Ideally we'd include a URL to this specific sync.
            // However, creating a workflow_dispatch event does not produce an ID: https://github.com/orgs/community/discussions/9752
            console.info(
              "Sync started in ${{ matrix.workflow_url }}"
            );
            console.info(
              "This may not start a new sync if one is already in progress. Check the logs of the workflow run."
            );
            if (immediatelyClose === 'true') {
              console.info(
                "Sync triggered with immediately-close=true due to test failure."
              );
            }
