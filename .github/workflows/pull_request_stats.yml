on:
  pull_request:
    types: [opened, synchronize]

name: Generate Pull Request Stats

env:
  NAPI_CLI_VERSION: 2.18.4
  TURBO_VERSION: 2.3.3
  NODE_LTS_VERSION: 20
  TEST_CONCURRENCY: 6

  TURBO_TEAM: 'vercel'
  TURBO_CACHE: 'remote:rw'
  NEXT_TELEMETRY_DISABLED: 1
  # we build a dev binary for use in CI so skip downloading
  # canary next-swc binaries in the monorepo
  NEXT_SKIP_NATIVE_POSTINSTALL: 1
  TEST_TIMINGS_TOKEN: ${{ secrets.TEST_TIMINGS_TOKEN }}
  NEXT_TEST_JOB: 1
  NEXT_DISABLE_SWC_WASM: 1

jobs:
  build:
    uses: ./.github/workflows/build_reusable.yml
    secrets: inherit
    with:
      stepName: 'generate-pull-request-stats'
      uploadSwcArtifact: 'yes'
      uploadAnalyzerArtifacts: 'yes'

  stats:
    name: PR Stats
    needs: build
    timeout-minutes: 25
    runs-on:
      - 'self-hosted'
      - 'linux'
      - 'x64'
      - 'metal'
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 25

      - name: Check non-docs only change
        run: echo "DOCS_CHANGE<<EOF" >> $GITHUB_OUTPUT; echo "$(node scripts/run-for-change.js --not --type docs --exec echo 'nope')" >> $GITHUB_OUTPUT; echo 'EOF' >> $GITHUB_OUTPUT
        id: docs-change

      - uses: actions/download-artifact@v4
        if: ${{ steps.docs-change.outputs.DOCS_CHANGE == 'nope' }}
        with:
          name: next-swc-binary
          path: packages/next-swc/native

      - run: cp -r packages/next-swc/native .github/actions/next-stats-action/native
        if: ${{ steps.docs-change.outputs.DOCS_CHANGE == 'nope' }}

      - uses: ./.github/actions/next-stats-action
        if: ${{ steps.docs-change.outputs.DOCS_CHANGE == 'nope' }}
