name: Rspack Next.js development integration tests

on:
  schedule:
    # Run an hour earlier than the turbopack tests, so we don't overwhelm the CI
    - cron: '0 5 * * *'
  workflow_dispatch: {}

jobs:
  test-dev:
    name: Rspack integration tests
    uses: ./.github/workflows/integration_tests_reusable.yml
    with:
      name: rspack-development
      test_type: development
      run_before_test: |
        export NEXT_RSPACK=1 NEXT_TEST_USE_RSPACK=1
      # Failing tests take longer (due to timeouts and retries). Since we have
      # many failing tests, we need smaller groups and longer timeouts, in case
      # a group gets stuck with a cluster of failing tests.
      e2e_groups: 16
      integration_groups: 16
      e2e_timeout_minutes: 90
      integration_timeout_minutes: 90
    secrets: inherit
