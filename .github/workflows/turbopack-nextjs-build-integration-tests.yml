name: Turbopack Next.js production integration tests

on:
  schedule:
    - cron: '0 6 * * *'
  workflow_dispatch: {}

jobs:
  test-dev:
    name: Next.js integration tests
    uses: ./.github/workflows/integration_tests_reusable.yml
    with:
      name: turbopack-production
      test_type: production
      run_before_test: |
        export IS_TURBOPACK_TEST=1 TURBOPACK_BUILD=1
      # Failing tests take longer (due to timeouts and retries). Since we have
      # many failing tests, we need smaller groups and longer timeouts, in case
      # a group gets stuck with a cluster of failing tests.
      e2e_groups: 12
      integration_groups: 12
      e2e_timeout_minutes: 60
      integration_timeout_minutes: 60
    secrets: inherit
