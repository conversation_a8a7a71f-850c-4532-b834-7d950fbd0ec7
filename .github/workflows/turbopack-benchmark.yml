name: Turbopack Benchmark

on:
  workflow_dispatch:
  push:
    branches:
      - canary
  pull_request:
    types: ['opened', 'reopened', 'synchronize', 'labeled']
    paths:
      - '**/crates/**'
      - '**/Cargo.toml'
      - '**/Cargo.lock'

concurrency:
  # Limit concurrent runs to 1 per PR, but allow concurrent runs on canary branch
  group: ${{ github.event_name == 'pull_request' && format('{0}-{1}', github.workflow, github.event.pull_request.number) || format('{0}-{1}-{2}', github.workflow, github.ref_name, github.run_id) }}
  cancel-in-progress: ${{ github.event_name == 'pull_request' }}

env:
  CI: 1
  CARGO_INCREMENTAL: 0
  # For faster CI
  RUST_LOG: 'off'
  TURBO_TEAM: 'vercel'
  TURBO_CACHE: 'remote:rw'
  TURBO_TOKEN: ${{ secrets.HOSTED_TURBO_TOKEN }}

jobs:
  benchmark-tiny:
    name: Benchmark Rust Crates (tiny)
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4

      - name: Setup Rust toolchain
        uses: ./.github/actions/setup-rust

      - name: Install cargo-codspeed
        uses: taiki-e/install-action@v2
        with:
          tool: cargo-codspeed@2.10.1

      - name: Build app build benchmarks
        run: cargo codspeed build -p next-api

      - name: Run the benchmarks
        uses: CodSpeedHQ/action@v3
        with:
          run: cargo codspeed run
          token: ${{ secrets.CODSPEED_TOKEN }}

  benchmark-small-apps:
    name: Benchmark Rust Crates (small apps)
    runs-on: ['self-hosted', 'linux', 'x64', 'metal']
    steps:
      - uses: actions/checkout@v4

      - name: Setup Rust toolchain
        uses: ./.github/actions/setup-rust

      - name: Install cargo-codspeed
        uses: taiki-e/install-action@v2
        with:
          tool: cargo-codspeed@2.10.1

      - name: Cache on ${{ github.ref_name }}
        uses: ijjk/rust-cache@turbo-cache-v1.0.9
        with:
          save-if: 'true'
          cache-provider: 'turbo'
          shared-key: build-turbopack-benchmark-small-apps-${{ hashFiles('.cargo/config.toml') }}

      - name: Install pnpm dependencies
        working-directory: turbopack/benchmark-apps
        run: |
          npm i -g corepack@0.31
          corepack enable
          pnpm install --loglevel error

      - name: Build app build benchmarks
        run: cargo codspeed build -p turbopack-cli small_apps

      - name: Run the benchmarks
        uses: CodSpeedHQ/action@v3
        with:
          run: cargo codspeed run
          token: ${{ secrets.CODSPEED_TOKEN }}

  benchmark-large:
    name: Benchmark Rust Crates (large)
    if: ${{ github.event.label.name == 'benchmark' || github.event_name == 'workflow_dispatch' }}
    runs-on: ['self-hosted', 'linux', 'x64', 'metal']
    steps:
      - uses: actions/checkout@v4

      - name: Setup Rust toolchain
        uses: ./.github/actions/setup-rust

      - name: Install cargo-codspeed
        uses: taiki-e/install-action@v2
        with:
          tool: cargo-codspeed@2.10.1

      - name: Build the benchmark target(s)
        run: cargo codspeed build -p turbopack -p turbopack-bench

      - name: Run the benchmarks
        uses: CodSpeedHQ/action@v3
        with:
          run: cargo codspeed run
          token: ${{ secrets.CODSPEED_TOKEN }}
