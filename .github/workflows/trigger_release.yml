on:
  schedule:
    # run every day at 23:15
    - cron: '15 23 * * *'

  workflow_dispatch:
    inputs:
      releaseType:
        description: stable, canary, or release candidate?
        required: true
        type: choice
        options:
          - canary
          - stable
          - release-candidate

      semverType:
        description: semver type?
        type: choice
        options:
          - patch
          - minor
          - major

      force:
        description: create a new release even if there are no new commits
        default: false
        type: boolean

    secrets:
      RELEASE_BOT_GITHUB_TOKEN:
        required: true

name: Trigger Release

env:
  NAPI_CLI_VERSION: 2.18.4
  TURBO_VERSION: 2.3.3
  NODE_LTS_VERSION: 20

jobs:
  start:
    if: github.repository_owner == 'vercel'
    runs-on: ubuntu-latest
    env:
      NEXT_TELEMETRY_DISABLED: 1
      # we build a dev binary for use in CI so skip downloading
      # canary next-swc binaries in the monorepo
      NEXT_SKIP_NATIVE_POSTINSTALL: 1

    environment: release-${{ github.event.inputs.releaseType || 'canary' }}
    steps:
      - name: Setup node
        uses: actions/setup-node@v4
        with:
          node-version: 18
          check-latest: true

      - name: <PERSON>lone Next.js repository
        run: git clone https://github.com/vercel/next.js.git --depth=25 --single-branch --branch ${GITHUB_REF_NAME:-canary} .

      - name: Check token
        run: gh auth status
        env:
          GITHUB_TOKEN: ${{ secrets.RELEASE_BOT_GITHUB_TOKEN }}

      - name: Get commit of the latest tag
        run: echo "LATEST_TAG_COMMIT=$(git rev-list -n 1 $(git describe --tags --abbrev=0))" >> $GITHUB_ENV

      - name: Get latest commit
        run: echo "LATEST_COMMIT=$(git rev-parse HEAD)" >> $GITHUB_ENV

      - name: Check if new commits since last tag
        if: ${{ github.event.inputs.releaseType != 'stable' && github.event.inputs.force != true }}
        run: |
          if [ "$LATEST_TAG_COMMIT" = "$LATEST_COMMIT" ]; then
            echo "No new commits. Exiting..."
            exit 1
          fi

      # https://github.com/actions/virtual-environments/issues/1187
      - name: tune linux network
        run: sudo ethtool -K eth0 tx off rx off

      - name: Setup corepack
        run: |
          npm i -g corepack@0.31
          corepack enable
          pnpm --version

      - id: get-store-path
        run: echo STORE_PATH=$(pnpm store path) >> $GITHUB_OUTPUT

      - uses: actions/cache@v4
        timeout-minutes: 5
        id: cache-pnpm-store
        with:
          path: ${{ steps.get-store-path.outputs.STORE_PATH }}
          key: pnpm-store-${{ hashFiles('pnpm-lock.yaml') }}
          restore-keys: |
            pnpm-store-
            pnpm-store-${{ hashFiles('pnpm-lock.yaml') }}

      - run: pnpm install

      - run: pnpm run build

      - run: node ./scripts/start-release.js --release-type ${{ github.event.inputs.releaseType || 'canary' }} --semver-type ${{ github.event.inputs.semverType }}
        env:
          RELEASE_BOT_GITHUB_TOKEN: ${{ secrets.RELEASE_BOT_GITHUB_TOKEN }}
