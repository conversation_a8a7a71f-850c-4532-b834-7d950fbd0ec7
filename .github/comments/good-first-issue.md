The issue was marked with the `good first issue` label by a maintainer.

This means that it is a good candidate for someone interested in contributing to the project, but does not know where to start.

To get started, read the [Contributing Guide](https://github.com/vercel/next.js/blob/canary/contributing.md). When you are ready, open a PR and link back to this issue in the form of adding `Fixes #1234` to the PR description, where `1234` is the issue number. This will automatically close the issue when the PR gets merged, making it easier for us to keep track of what has been fixed.

Please remember to add tests to confirm your code changes will fix the issue and we do not regress in the future.

If you have any questions, feel free to ask below or on the PR. Generally, you don't need to `@mention` anyone directly, as we will get notified anyway and will respond as soon as we can.

> [!NOTE]  
> There is no need to ask for permission "can I work on this?" Please, go ahead if there is no linked PR :slightly_smiling_face:
