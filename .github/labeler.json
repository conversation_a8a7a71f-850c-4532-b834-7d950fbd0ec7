{"labels": {"create-next-app": ["packages/create-next-app/**"], "documentation": ["docs/**", "errors/**"], "examples": ["examples/**"], "Font (next/font)": ["**/*font*"], "tests": ["test/**", "bench/**"], "Turbopack": ["crates/next-*/**", "crates/napi/**", "turbopack/**"], "Rspack": [{"type": "user", "pattern": "9aoy"}, {"type": "user", "pattern": "ahabhgk"}, {"type": "user", "pattern": "b<PERSON><PERSON><PERSON>"}, {"type": "user", "pattern": "<PERSON><PERSON><PERSON><PERSON>"}, {"type": "user", "pattern": "<PERSON><PERSON><PERSON>"}, {"type": "user", "pattern": "easy1090"}, {"type": "user", "pattern": "fi3ework"}, {"type": "user", "pattern": "GiveMe-A-Name"}, {"type": "user", "pattern": "h-a-n-a"}, {"type": "user", "pattern": "hardfist"}, {"type": "user", "pattern": "inottn"}, {"type": "user", "pattern": "jerry<PERSON><PERSON>z"}, {"type": "user", "pattern": "JSerFeng"}, {"type": "user", "pattern": "lingyucoder"}, {"type": "user", "pattern": "nyqykk"}, {"type": "user", "pattern": "sanyuan0704"}, {"type": "user", "pattern": "ScriptedAlchemy"}, {"type": "user", "pattern": "SoonIter"}, {"type": "user", "pattern": "stormslowly"}, {"type": "user", "pattern": "SyMind"}, {"type": "user", "pattern": "Timeless0911"}, {"type": "user", "pattern": "valorkin"}, {"type": "user", "pattern": "xc2"}, {"type": "user", "pattern": "zackarychapple"}, {"type": "user", "pattern": "zools<PERSON>"}, "packages/next/src/build/**"], "created-by: Chrome Aurora": [{"type": "user", "pattern": "atcastle"}, {"type": "user", "pattern": "devknoll"}, {"type": "user", "pattern": "<PERSON>usseindji<PERSON><PERSON>"}, {"type": "user", "pattern": "jani<PERSON><PERSON>-ralph"}, {"type": "user", "pattern": "kara"}, {"type": "user", "pattern": "kyliau"}, {"type": "user", "pattern": "spanicker"}], "CI approved": [{"type": "user", "pattern": "SukkaW"}, {"type": "user", "pattern": "nextjs-bot"}, {"type": "user", "pattern": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}], "created-by: Next.js team": [{"type": "user", "pattern": "acdlite"}, {"type": "user", "pattern": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"type": "user", "pattern": "eps1lon"}, {"type": "user", "pattern": "feedthejim"}, {"type": "user", "pattern": "<PERSON><PERSON><PERSON><PERSON>"}, {"type": "user", "pattern": "gnoff"}, {"type": "user", "pattern": "huo<PERSON>"}, {"type": "user", "pattern": "<PERSON><PERSON><PERSON>"}, {"type": "user", "pattern": "lazarv"}, {"type": "user", "pattern": "lubieo<PERSON><PERSON>"}, {"type": "user", "pattern": "bgub"}, {"type": "user", "pattern": "RobPruzan"}, {"type": "user", "pattern": "samcx"}, {"type": "user", "pattern": "sebmarkbage"}, {"type": "user", "pattern": "shuding"}, {"type": "user", "pattern": "styfle"}, {"type": "user", "pattern": "unstubbable"}, {"type": "user", "pattern": "wya<PERSON><PERSON><PERSON>"}, {"type": "user", "pattern": "<PERSON><PERSON><PERSON>"}], "created-by: Next.js DevEx team": [{"type": "user", "pattern": "delbaoliveira"}, {"type": "user", "pattern": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"type": "user", "pattern": "leerob"}, {"type": "user", "pattern": "manovotny"}, {"type": "user", "pattern": "molebox"}, {"type": "user", "pattern": "timeyoutakeit"}, {"type": "user", "pattern": "<PERSON><PERSON><PERSON><PERSON>"}], "created-by: Turbopack team": [{"type": "user", "pattern": "bgw"}, {"type": "user", "pattern": "Cy-<PERSON><PERSON>"}, {"type": "user", "pattern": "kdy1"}, {"type": "user", "pattern": "lukesandberg"}, {"type": "user", "pattern": "mischnic"}, {"type": "user", "pattern": "pad<PERSON>ia"}, {"type": "user", "pattern": "sokra"}, {"type": "user", "pattern": "timneutkens"}, {"type": "user", "pattern": "w<PERSON><PERSON><PERSON>"}], "type: next": ["packages/eslint-config-next/**", "packages/eslint-plugin-next/**", "packages/font/**", "packages/next-bundle-analyzer/**", "packages/next-codemod/**", "packages/next-env/**", "packages/next-mdx/**", "packages/next-swc/**", "packages/next/**", "packages/react-refresh-utils/**"]}}