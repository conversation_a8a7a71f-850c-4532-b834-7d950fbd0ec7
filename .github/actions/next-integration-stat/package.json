{"name": "next-integration-stat", "private": true, "main": "dist/index.js", "scripts": {"lint": "eslint src/", "build": "ncc build src/index.ts -t -o dist", "lint:prettier": "prettier -c . --cache --ignore-path=../../../.prettierignore"}, "devDependencies": {"@types/node": "^18.11.18", "@vercel/ncc": "0.34.0", "typescript": "^4.4.4"}, "dependencies": {"@actions/core": "^1.10.0", "@actions/exec": "^1.1.1", "@actions/github": "^6.0.0", "node-fetch": "^3.3.2", "semver": "^7.3.8", "strip-ansi": "^7.0.1"}, "engines": {"node": ">=18.18.0"}, "packageManager": "pnpm@9.6.0"}