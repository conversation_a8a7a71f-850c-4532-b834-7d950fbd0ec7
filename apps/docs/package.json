{"name": "next-docs", "version": "0.1.0", "private": true, "scripts": {"dev-docs": "next dev --turbopack", "build-docs": "next build", "start": "next start", "postinstall": "fumadocs-mdx"}, "dependencies": {"fumadocs-core": "15.6.0", "fumadocs-mdx": "11.6.10", "fumadocs-ui": "15.6.0", "next": "15.3.5", "react": "^19.0.0", "react-dom": "^19.0.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/mdx": "2.0.13", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.5", "tailwindcss": "^4", "typescript": "^5"}}