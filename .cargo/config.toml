[env]
CARGO_WORKSPACE_DIR = { value = "", relative = true }
TURBO_PNPM_WORKSPACE_DIR = { value = "", relative = true }

[alias]
xtask = "run --package xtask --"

[build]
rustflags = [
  "--cfg",
  "tokio_unstable",
  "-Zshare-generics=y", # make the current crate share its generic instantiations
  "-Zthreads=8", # parallel frontend https://blog.rust-lang.org/2023/11/09/parallel-rustc.html
  "-Csymbol-mangling-version=v0",
]
rustdocflags = []

[target.x86_64-pc-windows-msvc]
linker = "rust-lld"
rustflags = [
  "--cfg",
  "tokio_unstable",
  "-Zshare-generics=y",
  "-C",
  "target-feature=+crt-static"
]

[target.i686-pc-windows-msvc]
rustflags = [
  "--cfg",
  "tokio_unstable",
  "-Zshare-generics=y",
  "-C",
  "target-feature=+crt-static"
]

[target.'cfg(all(target_os = "linux", target_env = "gnu"))']
rustflags = [
  "--cfg",
  "tokio_unstable",
  "-Zshare-generics=y",
  "-Zthreads=8",
  "-Zunstable-options",
  "-Csymbol-mangling-version=v0",
  "-Clinker-flavor=gnu-lld-cc",
  "-Clink-self-contained=+linker",
]

[target.aarch64-unknown-linux-musl]
linker = "aarch64-linux-musl-gcc"
# Config need to be mirrowed to .github/workflows/build_and_deploy.yml
rustflags = [
  "--cfg",
  "tokio_unstable",
  "-Zshare-generics=y",
  "-Csymbol-mangling-version=v0",
  "-Ctarget-feature=-crt-static",
  "-Clink-arg=-lgcc",
]

[target.x86_64-unknown-linux-musl]
# Config need to be mirrowed to .github/workflows/build_and_deploy.yml
rustflags = [
  "--cfg",
  "tokio_unstable",
  "-Zshare-generics=y",
  "-Zthreads=8",
  "-Csymbol-mangling-version=v0",
  "-Ctarget-feature=-crt-static",
]

[target.armv7-unknown-linux-gnueabihf]
linker = "arm-linux-gnueabihf-gcc"

[target.wasm32-unknown-unknown]
rustflags = ["-Zshare-generics=y", "--cfg", 'getrandom_backend="wasm_js"']